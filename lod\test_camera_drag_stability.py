"""
测试相机拖动时的LOD稳定性
用于诊断和验证相机视角拖动时部分区域突然消失的问题修复
"""

import time
import math
from typing import Optional, Tuple
from pxr import Gf, UsdGeom
import omni.usd
from lod_scheduler import LODScheduler, TilesetLODManager, BoundingBox


class CameraDragStabilityTester:
    """相机拖动稳定性测试器"""
    
    def __init__(self, stage, camera_path: str = "/World/Camera"):
        self.stage = stage
        self.camera_path = camera_path
        self.test_results = []
        self.last_lod_state = None
        
    def get_camera_position(self) -> Optional[Gf.Vec3f]:
        """获取相机位置"""
        try:
            camera = self.stage.GetPrimAtPath(self.camera_path)
            if not camera:
                return None
            
            xformable = UsdGeom.Xformable(camera)
            world_transform = xformable.ComputeLocalToWorldTransform()
            position = Gf.Vec3f(world_transform[3][0], world_transform[3][1], world_transform[3][2])
            return position
        except Exception as e:
            print(f"Error getting camera position: {e}")
            return None
    
    def simulate_camera_drag(self, start_pos: Gf.Vec3f, end_pos: Gf.Vec3f, 
                           steps: int = 20, test_duration: float = 5.0):
        """
        模拟相机拖动过程
        
        Args:
            start_pos: 起始位置
            end_pos: 结束位置
            steps: 拖动步数
            test_duration: 测试持续时间（秒）
        """
        print(f"\n=== 开始相机拖动稳定性测试 ===")
        print(f"起始位置: {start_pos}")
        print(f"结束位置: {end_pos}")
        print(f"拖动步数: {steps}")
        print(f"测试时长: {test_duration}秒")
        
        # 获取相机prim
        camera = self.stage.GetPrimAtPath(self.camera_path)
        if not camera:
            print("ERROR: Camera not found")
            return
        
        xformable = UsdGeom.Xformable(camera)
        
        # 记录测试开始时间
        start_time = time.time()
        step_interval = test_duration / steps
        
        # 执行拖动测试
        for i in range(steps + 1):
            # 计算当前位置（线性插值）
            t = i / steps
            current_pos = Gf.Vec3f(
                start_pos[0] + t * (end_pos[0] - start_pos[0]),
                start_pos[1] + t * (end_pos[1] - start_pos[1]),
                start_pos[2] + t * (end_pos[2] - start_pos[2])
            )
            
            # 设置相机位置
            transform = xformable.GetLocalTransformation()
            transform.SetTranslateOnly(current_pos)
            xformable.SetXformOpOrder([xformable.GetOrderedXformOps()[0]])
            
            # 记录当前状态
            self._record_test_state(i, current_pos, time.time() - start_time)
            
            # 等待下一步
            if i < steps:
                time.sleep(step_interval)
        
        print(f"\n=== 相机拖动测试完成 ===")
        self._analyze_test_results()
    
    def _record_test_state(self, step: int, camera_pos: Gf.Vec3f, elapsed_time: float):
        """记录测试状态"""
        try:
            # 这里可以添加LOD状态检测逻辑
            # 由于我们已经注释了LOD更新逻辑，这里主要记录相机位置变化
            
            state = {
                'step': step,
                'camera_pos': camera_pos,
                'elapsed_time': elapsed_time,
                'timestamp': time.time()
            }
            
            self.test_results.append(state)
            
            if step % 5 == 0:  # 每5步输出一次状态
                print(f"Step {step:2d}: Camera at {camera_pos}, Time: {elapsed_time:.2f}s")
                
        except Exception as e:
            print(f"Error recording test state at step {step}: {e}")
    
    def _analyze_test_results(self):
        """分析测试结果"""
        if not self.test_results:
            print("No test results to analyze")
            return
        
        print(f"\n📊 测试结果分析:")
        print(f"总步数: {len(self.test_results)}")
        
        # 计算相机移动距离
        if len(self.test_results) >= 2:
            start_pos = self.test_results[0]['camera_pos']
            end_pos = self.test_results[-1]['camera_pos']
            total_distance = math.sqrt(
                (end_pos[0] - start_pos[0])**2 +
                (end_pos[1] - start_pos[1])**2 +
                (end_pos[2] - start_pos[2])**2
            )
            print(f"总移动距离: {total_distance:.2f}m")
            
            # 计算平均移动速度
            total_time = self.test_results[-1]['elapsed_time']
            avg_speed = total_distance / total_time if total_time > 0 else 0
            print(f"平均移动速度: {avg_speed:.2f}m/s")
        
        # 检查是否有异常的时间间隔
        time_intervals = []
        for i in range(1, len(self.test_results)):
            interval = self.test_results[i]['elapsed_time'] - self.test_results[i-1]['elapsed_time']
            time_intervals.append(interval)
        
        if time_intervals:
            avg_interval = sum(time_intervals) / len(time_intervals)
            max_interval = max(time_intervals)
            min_interval = min(time_intervals)
            
            print(f"平均时间间隔: {avg_interval:.3f}s")
            print(f"最大时间间隔: {max_interval:.3f}s")
            print(f"最小时间间隔: {min_interval:.3f}s")
            
            # 检查是否有异常的时间间隔（可能表示渲染卡顿）
            threshold = avg_interval * 2
            anomalies = [i for i, interval in enumerate(time_intervals) if interval > threshold]
            if anomalies:
                print(f"⚠️  发现 {len(anomalies)} 个异常时间间隔（可能的卡顿点）:")
                for idx in anomalies[:5]:  # 只显示前5个
                    print(f"  Step {idx+1}: {time_intervals[idx]:.3f}s")


def test_camera_drag_stability():
    """执行相机拖动稳定性测试"""
    try:
        # 获取当前stage
        stage = omni.usd.get_context().get_stage()
        if not stage:
            print("ERROR: No active USD stage found")
            return
        
        # 创建测试器
        tester = CameraDragStabilityTester(stage)
        
        # 获取当前相机位置作为起始点
        current_pos = tester.get_camera_position()
        if not current_pos:
            print("ERROR: Could not get current camera position")
            return
        
        print(f"当前相机位置: {current_pos}")
        
        # 定义测试路径（从当前位置向不同方向移动）
        test_scenarios = [
            {
                'name': '水平移动测试',
                'start': current_pos,
                'end': Gf.Vec3f(current_pos[0] + 50, current_pos[1], current_pos[2]),
                'steps': 25,
                'duration': 3.0
            },
            {
                'name': '垂直移动测试', 
                'start': current_pos,
                'end': Gf.Vec3f(current_pos[0], current_pos[1], current_pos[2] + 30),
                'steps': 20,
                'duration': 2.5
            },
            {
                'name': '对角线移动测试',
                'start': current_pos,
                'end': Gf.Vec3f(current_pos[0] + 30, current_pos[1] + 30, current_pos[2] + 20),
                'steps': 30,
                'duration': 4.0
            }
        ]
        
        # 执行测试场景
        for scenario in test_scenarios:
            print(f"\n{'='*50}")
            print(f"执行测试场景: {scenario['name']}")
            print(f"{'='*50}")
            
            tester.simulate_camera_drag(
                scenario['start'],
                scenario['end'], 
                scenario['steps'],
                scenario['duration']
            )
            
            # 测试间隔
            print("等待2秒后进行下一个测试...")
            time.sleep(2.0)
        
        print(f"\n✅ 所有相机拖动稳定性测试完成")
        print(f"💡 如果在测试过程中观察到区域突然消失，请检查:")
        print(f"   1. LOD更新频率是否过低")
        print(f"   2. LOD切换边界是否存在抖动")
        print(f"   3. 可见性更新是否为原子性操作")
        print(f"   4. 是否存在缓存失效问题")
        
    except Exception as e:
        print(f"ERROR: Camera drag stability test failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_camera_drag_stability()
