"""
应用相机拖动问题修复
使用示例脚本，展示如何应用修复方案
"""

import omni.usd
from camera_drag_fix_config import (
    apply_camera_drag_fixes, 
    get_optimized_lod_config,
    diagnose_camera_drag_issues,
    get_performance_monitor
)


def apply_fixes_to_existing_system():
    """对现有系统应用相机拖动修复"""
    try:
        print("🚀 开始应用相机拖动问题修复...")
        
        # 1. 获取当前stage
        stage = omni.usd.get_context().get_stage()
        if not stage:
            print("❌ 错误: 未找到活跃的USD stage")
            return False
        
        print("✅ 找到活跃的USD stage")
        
        # 2. 诊断现有问题
        print("\n🔍 诊断现有问题...")
        issues = diagnose_camera_drag_issues(stage)
        
        # 3. 获取优化配置
        config = get_optimized_lod_config()
        print(f"\n⚙️  使用优化配置:")
        print(f"   LOD更新间隔: {config.lod_update_interval}s")
        print(f"   边界滞后因子: {config.hysteresis_factor}")
        print(f"   最小距离变化阈值: {config.min_distance_change_threshold}m")
        print(f"   LOD切换冷却期: {config.lod_change_cooldown}s")
        
        # 4. 查找现有的LOD管理器
        # 注意：这里需要根据你的实际系统调整
        tileset_manager = find_existing_tileset_manager()
        
        if tileset_manager:
            print("✅ 找到现有的Tileset LOD管理器")
            
            # 应用修复
            apply_camera_drag_fixes(tileset_manager, config)
            
            # 启动性能监控
            monitor = get_performance_monitor()
            print("📊 性能监控已启动")
            
        else:
            print("⚠️  未找到现有的LOD管理器")
            print("💡 建议: 请确保LOD系统已正确初始化")
            return False
        
        print("\n✅ 相机拖动问题修复已成功应用!")
        print("💡 现在可以测试相机拖动，观察是否还有区域突然消失的问题")
        
        return True
        
    except Exception as e:
        print(f"❌ 应用修复时发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False


def find_existing_tileset_manager():
    """查找现有的Tileset LOD管理器"""
    # 这里需要根据你的实际系统实现
    # 可能的查找方式：
    # 1. 从全局变量中获取
    # 2. 从某个管理类中获取
    # 3. 重新创建一个管理器实例
    
    try:
        # 示例：尝试从全局变量获取（需要根据实际情况调整）
        import sys
        
        # 检查是否有全局的tileset管理器
        if hasattr(sys.modules.get('__main__', None), 'tileset_manager'):
            return sys.modules['__main__'].tileset_manager
        
        # 或者尝试从其他模块获取
        # 这里需要根据你的实际代码结构调整
        
        print("ℹ️  未找到现有管理器，尝试创建新的管理器...")
        return create_new_tileset_manager()
        
    except Exception as e:
        print(f"查找LOD管理器时出错: {e}")
        return None


def create_new_tileset_manager():
    """创建新的Tileset LOD管理器"""
    try:
        from lod_scheduler import TilesetLODManager, LODScheduler
        from lod_config import get_default_lod_config
        
        stage = omni.usd.get_context().get_stage()
        if not stage:
            return None
        
        # 创建LOD调度器
        lod_config = get_default_lod_config()
        scheduler = LODScheduler(stage, centralized_config=lod_config)
        
        # 创建Tileset管理器
        manager = TilesetLODManager(
            stage=stage,
            camera_path="/World/Camera",
            lod_scheduler=scheduler,
            config=lod_config
        )
        
        print("✅ 创建了新的Tileset LOD管理器")
        return manager
        
    except Exception as e:
        print(f"创建新管理器时出错: {e}")
        return None


def test_fix_effectiveness():
    """测试修复效果"""
    print("\n🧪 测试修复效果...")
    
    try:
        from test_camera_drag_stability import test_camera_drag_stability
        
        print("执行相机拖动稳定性测试...")
        test_camera_drag_stability()
        
        print("✅ 测试完成，请观察是否还有区域突然消失的问题")
        
    except Exception as e:
        print(f"测试时出错: {e}")


def show_monitoring_info():
    """显示监控信息"""
    try:
        monitor = get_performance_monitor()
        stats = monitor.get_statistics()
        
        print(f"\n📊 性能监控信息:")
        print(f"   LOD变更次数: {stats['lod_changes']}")
        print(f"   相机位置记录: {stats['camera_positions_recorded']}")
        print(f"   最后变更时间: {stats['last_change_time']}")
        
    except Exception as e:
        print(f"获取监控信息时出错: {e}")


def main():
    """主函数"""
    print("🔧 相机拖动问题修复工具")
    print("="*50)
    
    # 应用修复
    success = apply_fixes_to_existing_system()
    
    if success:
        print("\n选择下一步操作:")
        print("1. 测试修复效果")
        print("2. 显示监控信息") 
        print("3. 退出")
        
        try:
            choice = input("\n请输入选择 (1-3): ").strip()
            
            if choice == "1":
                test_fix_effectiveness()
            elif choice == "2":
                show_monitoring_info()
            elif choice == "3":
                print("👋 退出")
            else:
                print("无效选择")
                
        except KeyboardInterrupt:
            print("\n👋 用户中断，退出")
        except Exception as e:
            print(f"处理用户输入时出错: {e}")
    
    else:
        print("\n❌ 修复应用失败，请检查系统状态")


if __name__ == "__main__":
    main()
