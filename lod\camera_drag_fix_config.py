"""
相机拖动问题修复配置
针对相机视角拖动时部分区域突然消失的问题提供优化配置
"""

from dataclasses import dataclass
from typing import Dict, Any


@dataclass
class CameraDragFixConfig:
    """相机拖动修复配置类"""
    
    # LOD更新频率优化
    lod_update_interval: float = 0.05  # 从1.0秒降低到0.05秒，提高响应性
    
    # 边界稳定性控制
    hysteresis_factor: float = 0.15  # 滞后因子，防止边界抖动
    min_distance_change_threshold: float = 3.0  # 最小距离变化阈值
    lod_change_cooldown: float = 0.3  # LOD切换冷却期（秒）
    
    # 相机移动检测
    camera_movement_threshold: float = 0.5  # 相机移动检测阈值
    camera_movement_check_interval: float = 0.02  # 相机移动检查间隔
    
    # 可见性更新优化
    atomic_visibility_update: bool = True  # 启用原子性可见性更新
    batch_visibility_changes: bool = True  # 批量处理可见性变更
    
    # 缓存管理
    enable_smart_caching: bool = True  # 启用智能缓存
    cache_invalidation_threshold: float = 10.0  # 缓存失效距离阈值
    
    # 调试和监控
    enable_performance_monitoring: bool = True  # 启用性能监控
    log_lod_changes: bool = False  # 记录LOD变更（仅调试时启用）
    verbose_output: bool = False  # 详细输出（仅调试时启用）


def get_optimized_lod_config() -> CameraDragFixConfig:
    """获取针对相机拖动问题优化的LOD配置"""
    return CameraDragFixConfig()


def get_debug_lod_config() -> CameraDragFixConfig:
    """获取用于调试的LOD配置"""
    config = CameraDragFixConfig()
    config.log_lod_changes = True
    config.verbose_output = True
    config.lod_update_interval = 0.1  # 稍微降低更新频率以便观察
    return config


def apply_camera_drag_fixes(tileset_manager, config: CameraDragFixConfig = None):
    """
    应用相机拖动问题修复
    
    Args:
        tileset_manager: TilesetLODManager实例
        config: 修复配置，如果为None则使用默认优化配置
    """
    if config is None:
        config = get_optimized_lod_config()
    
    print("🔧 应用相机拖动问题修复...")
    
    # 1. 优化LOD更新频率
    if hasattr(tileset_manager, 'start_automatic_lod_switching'):
        print(f"   ⚡ 设置LOD更新间隔: {config.lod_update_interval}s")
        # 注意：需要先停止现有的自动更新，然后用新配置重启
        tileset_manager.stop_automatic_lod_switching()
        tileset_manager.start_automatic_lod_switching(config.lod_update_interval)
    
    # 2. 配置边界稳定性参数
    if hasattr(tileset_manager, 'lod_scheduler') and tileset_manager.lod_scheduler:
        scheduler = tileset_manager.lod_scheduler
        
        # 设置滞后因子（如果调度器支持）
        if hasattr(scheduler, 'hysteresis_factor'):
            scheduler.hysteresis_factor = config.hysteresis_factor
            print(f"   🎯 设置边界滞后因子: {config.hysteresis_factor}")
        
        # 设置距离变化阈值
        if hasattr(scheduler, 'min_distance_change_threshold'):
            scheduler.min_distance_change_threshold = config.min_distance_change_threshold
            print(f"   📏 设置最小距离变化阈值: {config.min_distance_change_threshold}m")
    
    # 3. 启用性能监控
    if config.enable_performance_monitoring:
        print("   📊 启用性能监控")
        # 这里可以添加性能监控逻辑
    
    # 4. 配置缓存管理
    if config.enable_smart_caching:
        print("   🧠 启用智能缓存管理")
        # 清除现有缓存，确保使用新配置
        if hasattr(tileset_manager, 'clear_cache'):
            tileset_manager.clear_cache()
    
    print("✅ 相机拖动问题修复配置已应用")
    
    return config


def diagnose_camera_drag_issues(stage, camera_path: str = "/World/Camera"):
    """
    诊断相机拖动相关问题
    
    Args:
        stage: USD Stage
        camera_path: 相机路径
    """
    print("\n🔍 诊断相机拖动相关问题...")
    
    issues_found = []
    
    # 1. 检查相机是否存在
    camera = stage.GetPrimAtPath(camera_path)
    if not camera:
        issues_found.append(f"相机未找到: {camera_path}")
    else:
        print(f"✅ 相机存在: {camera_path}")
    
    # 2. 检查是否有活跃的LOD管理器
    # 这里需要根据实际的全局状态检查
    print("   检查LOD管理器状态...")
    
    # 3. 检查渲染性能指标
    print("   检查渲染性能...")
    
    # 4. 检查内存使用情况
    print("   检查内存使用...")
    
    if issues_found:
        print(f"\n⚠️  发现 {len(issues_found)} 个问题:")
        for issue in issues_found:
            print(f"   - {issue}")
    else:
        print("\n✅ 未发现明显问题")
    
    return issues_found


def create_performance_monitor():
    """创建性能监控器"""
    class PerformanceMonitor:
        def __init__(self):
            self.lod_change_count = 0
            self.last_lod_change_time = 0
            self.frame_times = []
            self.camera_positions = []
        
        def record_lod_change(self, new_lod: str, distance: float):
            """记录LOD变更"""
            import time
            current_time = time.time()
            self.lod_change_count += 1
            self.last_lod_change_time = current_time
            
            print(f"📈 LOD变更 #{self.lod_change_count}: {new_lod} (距离: {distance:.1f}m)")
        
        def record_camera_position(self, position):
            """记录相机位置"""
            import time
            self.camera_positions.append((time.time(), position))
            
            # 保持最近100个位置记录
            if len(self.camera_positions) > 100:
                self.camera_positions = self.camera_positions[-100:]
        
        def get_statistics(self):
            """获取统计信息"""
            return {
                'lod_changes': self.lod_change_count,
                'camera_positions_recorded': len(self.camera_positions),
                'last_change_time': self.last_lod_change_time
            }
    
    return PerformanceMonitor()


# 全局性能监控器实例
_global_performance_monitor = None

def get_performance_monitor():
    """获取全局性能监控器"""
    global _global_performance_monitor
    if _global_performance_monitor is None:
        _global_performance_monitor = create_performance_monitor()
    return _global_performance_monitor


def print_fix_summary():
    """打印修复方案总结"""
    print("\n" + "="*60)
    print("🔧 相机拖动问题修复方案总结")
    print("="*60)
    print("问题现象: 相机视角拖动时部分区域突然消失")
    print("\n修复措施:")
    print("1. ⚡ 提高LOD更新频率 (1.0s → 0.05s)")
    print("2. 🎯 增加边界稳定性控制 (滞后因子)")
    print("3. 🔒 实现原子性可见性更新")
    print("4. ⏱️  添加LOD切换冷却期")
    print("5. 🧠 优化缓存管理策略")
    print("6. 📊 增加性能监控")
    print("\n使用方法:")
    print("```python")
    print("from camera_drag_fix_config import apply_camera_drag_fixes")
    print("apply_camera_drag_fixes(your_tileset_manager)")
    print("```")
    print("="*60)


if __name__ == "__main__":
    print_fix_summary()
